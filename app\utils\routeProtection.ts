// Route protection utilities for role-based access control

import { getAccessibleRoutes, hasAccess, navigationPermissions } from './rolePermissions';

// Define protected routes and their required roles
export const protectedRoutes = {
  // All Reports routes
  '/dashboard/all-enrolment-reports': navigationPermissions.allReports.enrolment,
  '/dashboard/all-tagging-reports': navigationPermissions.allReports.tagging,
  '/dashboard/all-student-results-reports': navigationPermissions.allReports.studentResults,
  '/dashboard/all-billing-reports': navigationPermissions.allReports.billing,
  '/dashboard/all-graduation-reports': navigationPermissions.allReports.graduation,
  '/dashboard/all-cop-tagging-reports': navigationPermissions.allReports.copTagging,
  
  // Individual Reports routes
  '/dashboard/enrolment-reports': navigationPermissions.individualReports.enrolment,
  '/dashboard/tagging-reports': navigationPermissions.individualReports.tagging,
  '/dashboard/student-results-reports': navigationPermissions.individualReports.studentResults,
  
  // Admin Actions routes
  '/user-management': navigationPermissions.adminActions.userManagement,
  '/activity-log': navigationPermissions.adminActions.activityLog,
  '/lookups': navigationPermissions.adminActions.lookups,
  '/reports-settings': navigationPermissions.adminActions.reportsSettings,
};

// Check if user can access a specific route
export const canAccessRoute = (userRole: string | undefined, route: string): boolean => {
  // Dashboard is accessible to all authenticated users
  if (route === '/dashboard') {
    return hasAccess(userRole, navigationPermissions.dashboard);
  }
  
  // Check if route is protected
  const requiredRoles = protectedRoutes[route as keyof typeof protectedRoutes];
  if (!requiredRoles) {
    // Route is not protected, allow access
    return true;
  }
  
  return hasAccess(userRole, requiredRoles);
};

// Get redirect route for unauthorized access
export const getRedirectRoute = (userRole: string | undefined): string => {
  // If user has no role, redirect to login
  if (!userRole) {
    return '/login';
  }
  
  // Get accessible routes for the user
  const accessibleRoutes = getAccessibleRoutes(userRole);
  
  // If user can access dashboard, redirect there
  if (accessibleRoutes.includes('/dashboard')) {
    return '/dashboard';
  }
  
  // If user has any accessible routes, redirect to the first one
  if (accessibleRoutes.length > 0) {
    return accessibleRoutes[0];
  }
  
  // Fallback to forbidden page
  return '/forbidden';
};

// Client-side route protection hook
export const useRouteProtection = (currentRoute: string, userRole: string | undefined) => {
  const canAccess = canAccessRoute(userRole, currentRoute);
  const redirectRoute = canAccess ? null : getRedirectRoute(userRole);
  
  return {
    canAccess,
    redirectRoute,
    shouldRedirect: !canAccess && redirectRoute !== currentRoute,
  };
};

// Get user-friendly error message for unauthorized access
export const getUnauthorizedMessage = (userRole: string | undefined, route: string): string => {
  const requiredRoles = protectedRoutes[route as keyof typeof protectedRoutes];
  
  if (!requiredRoles) {
    return 'Access denied to this resource.';
  }
  
  const roleNames = requiredRoles.join(', ');
  
  if (!userRole) {
    return `This page requires authentication. Please log in with one of these roles: ${roleNames}`;
  }
  
  return `Your role (${userRole}) does not have access to this page. Required roles: ${roleNames}`;
};
