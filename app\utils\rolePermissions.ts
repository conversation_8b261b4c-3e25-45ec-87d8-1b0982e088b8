// Role-based access control utilities

export type UserRole = "SuperAdmin" | "DepartmentAdmin" | "Officer" | "Student" | "University";

export interface RolePermissions {
  dashboard: UserRole[];
  allReports: {
    enrolment: UserRole[];
    tagging: UserRole[];
    studentResults: UserRole[];
    billing: UserRole[];
    graduation: UserRole[];
    copTagging: UserRole[];
  };
  individualReports: {
    enrolment: UserRole[];
    tagging: UserRole[];
    studentResults: UserRole[];
  };
  adminActions: {
    userManagement: UserRole[];
    activityLog: UserRole[];
    lookups: UserRole[];
    reportsSettings: UserRole[];
  };
}

// Define role-based navigation permissions
export const navigationPermissions: RolePermissions = {
  dashboard: ["SuperAdmin", "DepartmentAdmin", "Officer", "Student", "University"],
  allReports: {
    enrolment: ["SuperAdmin", "DepartmentAdmin", "Officer"],
    tagging: ["SuperAdmin", "DepartmentAdmin", "Officer"],
    studentResults: ["SuperAdmin", "DepartmentAdmin", "Officer"],
    billing: ["SuperAdmin", "DepartmentAdmin"],
    graduation: ["SuperAdmin", "DepartmentAdmin", "Officer"],
    copTagging: ["SuperAdmin", "DepartmentAdmin", "Officer"],
  },
  individualReports: {
    enrolment: ["SuperAdmin", "DepartmentAdmin", "Officer", "University"],
    tagging: ["SuperAdmin", "DepartmentAdmin", "Officer", "University"],
    studentResults: ["SuperAdmin", "DepartmentAdmin", "Officer", "University"],
  },
  adminActions: {
    userManagement: ["SuperAdmin"],
    activityLog: ["SuperAdmin", "DepartmentAdmin"],
    lookups: ["SuperAdmin", "DepartmentAdmin"],
    reportsSettings: ["SuperAdmin", "DepartmentAdmin"],
  },
};

// Helper function to check if user has access to a specific feature
export const hasAccess = (userRole: string | undefined, allowedRoles: UserRole[]): boolean => {
  if (!userRole) return false;
  return allowedRoles.includes(userRole as UserRole);
};

// Helper function to check if user has any admin privileges
export const isAdmin = (userRole: string | undefined): boolean => {
  return hasAccess(userRole, ["SuperAdmin", "DepartmentAdmin"]);
};

// Helper function to check if user is SuperAdmin
export const isSuperAdmin = (userRole: string | undefined): boolean => {
  return hasAccess(userRole, ["SuperAdmin"]);
};

// Helper function to get role display name with styling info
export const getRoleDisplayInfo = (role: string) => {
  const roleInfo = {
    SuperAdmin: { display: "Super Administrator", color: "red", priority: 1 },
    DepartmentAdmin: { display: "Department Administrator", color: "orange", priority: 2 },
    Officer: { display: "Officer", color: "blue", priority: 3 },
    Student: { display: "Student", color: "green", priority: 4 },
    University: { display: "University", color: "purple", priority: 5 },
  };
  
  return roleInfo[role as keyof typeof roleInfo] || { 
    display: role, 
    color: "gray", 
    priority: 999 
  };
};

// Role-specific welcome messages
export const getRoleSpecificWelcomeMessage = (role: string): string => {
  const roleMessages = {
    SuperAdmin: [
      "System ready", "All systems go", "Full access granted", "Command center active",
      "Master control", "System administrator", "Full privileges", "Complete access"
    ],
    DepartmentAdmin: [
      "Department ready", "Managing well", "Department active", "Admin dashboard",
      "Department control", "Management ready", "Admin access", "Department online"
    ],
    Officer: [
      "Reports ready", "Data available", "System operational", "Officer dashboard",
      "Reports active", "Data ready", "System ready", "Officer online"
    ],
    Student: [
      "Welcome student", "Ready to learn", "Access granted", "Student portal",
      "Learning ready", "Student access", "Portal active", "Ready to go"
    ],
    University: [
      "Institution ready", "Upload ready", "System active", "University portal",
      "Institution online", "Upload system", "University access", "Portal ready"
    ],
  };
  
  const messages = roleMessages[role as keyof typeof roleMessages] || [
    "Welcome", "Hello", "Hi", "Ready to go", "System ready"
  ];
  
  return messages[Math.floor(Math.random() * messages.length)];
};

// Function to get accessible routes for a user role
export const getAccessibleRoutes = (userRole: string | undefined): string[] => {
  if (!userRole) return [];
  
  const routes: string[] = [];
  
  // Dashboard is accessible to all authenticated users
  if (hasAccess(userRole, navigationPermissions.dashboard)) {
    routes.push("/dashboard");
  }
  
  // All Reports routes
  if (hasAccess(userRole, navigationPermissions.allReports.enrolment)) {
    routes.push("/dashboard/all-enrolment-reports");
  }
  if (hasAccess(userRole, navigationPermissions.allReports.tagging)) {
    routes.push("/dashboard/all-tagging-reports");
  }
  if (hasAccess(userRole, navigationPermissions.allReports.studentResults)) {
    routes.push("/dashboard/all-student-results-reports");
  }
  if (hasAccess(userRole, navigationPermissions.allReports.billing)) {
    routes.push("/dashboard/all-billing-reports");
  }
  if (hasAccess(userRole, navigationPermissions.allReports.graduation)) {
    routes.push("/dashboard/all-graduation-reports");
  }
  if (hasAccess(userRole, navigationPermissions.allReports.copTagging)) {
    routes.push("/dashboard/all-cop-tagging-reports");
  }
  
  // Individual Reports routes
  if (hasAccess(userRole, navigationPermissions.individualReports.enrolment)) {
    routes.push("/dashboard/enrolment-reports");
  }
  if (hasAccess(userRole, navigationPermissions.individualReports.tagging)) {
    routes.push("/dashboard/tagging-reports");
  }
  if (hasAccess(userRole, navigationPermissions.individualReports.studentResults)) {
    routes.push("/dashboard/student-results-reports");
  }
  
  // Admin Actions routes
  if (hasAccess(userRole, navigationPermissions.adminActions.userManagement)) {
    routes.push("/user-management");
  }
  if (hasAccess(userRole, navigationPermissions.adminActions.activityLog)) {
    routes.push("/activity-log");
  }
  if (hasAccess(userRole, navigationPermissions.adminActions.lookups)) {
    routes.push("/lookups");
  }
  if (hasAccess(userRole, navigationPermissions.adminActions.reportsSettings)) {
    routes.push("/reports-settings");
  }
  
  return routes;
};
