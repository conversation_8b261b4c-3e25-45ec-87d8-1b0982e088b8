"use client";

import { Container, Title, Text, Card, Box, Badge, List } from "@mantine/core";
import { useAuth } from "../../hooks/useAuth";
import { hasAccess, navigationPermissions, getAccessibleRoutes } from "../../utils/rolePermissions";

export default function RoleTestPage() {
  const { user, loading } = useAuth();
  
  if (loading) {
    return (
      <Container>
        <Text>Loading...</Text>
      </Container>
    );
  }

  // Handle both possible user object structures
  const userRole = user?.role?.roleName || (user as any)?.roleName;
  const userName = user?.name || "Unknown User";
  const userEmail = user?.email || user?.username || "No email";

  const accessibleRoutes = getAccessibleRoutes(userRole);

  return (
    <Container size="lg" py="xl">
      <Title order={1} mb="xl">Role-Based Access Control Test</Title>
      
      <Card shadow="sm" padding="lg" radius="md" withBorder mb="xl">
        <Title order={3} mb="md">User Information</Title>
        <Box>
          <Text><strong>Name:</strong> {userName}</Text>
          <Text><strong>Email:</strong> {userEmail}</Text>
          <Text><strong>Role:</strong> {userRole ? <Badge color="blue">{userRole}</Badge> : <Badge color="gray">No Role</Badge>}</Text>
        </Box>
      </Card>

      <Card shadow="sm" padding="lg" radius="md" withBorder mb="xl">
        <Title order={3} mb="md">Access Permissions</Title>
        <Box>
          <Text mb="sm"><strong>Dashboard Access:</strong> {hasAccess(userRole, navigationPermissions.dashboard) ? "✅ Yes" : "❌ No"}</Text>
          
          <Text mb="sm"><strong>All Reports Access:</strong></Text>
          <List size="sm" ml="md" mb="md">
            <List.Item>Enrolment: {hasAccess(userRole, navigationPermissions.allReports.enrolment) ? "✅" : "❌"}</List.Item>
            <List.Item>Tagging: {hasAccess(userRole, navigationPermissions.allReports.tagging) ? "✅" : "❌"}</List.Item>
            <List.Item>Student Results: {hasAccess(userRole, navigationPermissions.allReports.studentResults) ? "✅" : "❌"}</List.Item>
            <List.Item>Billing: {hasAccess(userRole, navigationPermissions.allReports.billing) ? "✅" : "❌"}</List.Item>
            <List.Item>Graduation: {hasAccess(userRole, navigationPermissions.allReports.graduation) ? "✅" : "❌"}</List.Item>
            <List.Item>COP Tagging: {hasAccess(userRole, navigationPermissions.allReports.copTagging) ? "✅" : "❌"}</List.Item>
          </List>

          <Text mb="sm"><strong>Individual Reports Access:</strong></Text>
          <List size="sm" ml="md" mb="md">
            <List.Item>Enrolment: {hasAccess(userRole, navigationPermissions.individualReports.enrolment) ? "✅" : "❌"}</List.Item>
            <List.Item>Tagging: {hasAccess(userRole, navigationPermissions.individualReports.tagging) ? "✅" : "❌"}</List.Item>
            <List.Item>Student Results: {hasAccess(userRole, navigationPermissions.individualReports.studentResults) ? "✅" : "❌"}</List.Item>
          </List>

          <Text mb="sm"><strong>Admin Actions Access:</strong></Text>
          <List size="sm" ml="md" mb="md">
            <List.Item>User Management: {hasAccess(userRole, navigationPermissions.adminActions.userManagement) ? "✅" : "❌"}</List.Item>
            <List.Item>Activity Log: {hasAccess(userRole, navigationPermissions.adminActions.activityLog) ? "✅" : "❌"}</List.Item>
            <List.Item>Lookups: {hasAccess(userRole, navigationPermissions.adminActions.lookups) ? "✅" : "❌"}</List.Item>
            <List.Item>Reports Settings: {hasAccess(userRole, navigationPermissions.adminActions.reportsSettings) ? "✅" : "❌"}</List.Item>
          </List>
        </Box>
      </Card>

      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Title order={3} mb="md">Accessible Routes</Title>
        <Text mb="sm">Based on your role, you have access to these routes:</Text>
        {accessibleRoutes.length > 0 ? (
          <List size="sm">
            {accessibleRoutes.map((route, index) => (
              <List.Item key={index}>{route}</List.Item>
            ))}
          </List>
        ) : (
          <Text color="dimmed">No accessible routes found.</Text>
        )}
      </Card>

      <Card shadow="sm" padding="lg" radius="md" withBorder mt="xl">
        <Title order={3} mb="md">Raw User Object</Title>
        <Text size="xs" style={{ fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
          {JSON.stringify(user, null, 2)}
        </Text>
      </Card>
    </Container>
  );
}
