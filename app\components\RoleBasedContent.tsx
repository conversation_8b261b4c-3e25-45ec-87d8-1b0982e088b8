"use client";

import { ReactNode } from 'react';
import { useAuth } from '../hooks/useAuth';
import { hasAccess, UserRole } from '../utils/rolePermissions';

interface RoleBasedContentProps {
  children: ReactNode;
  allowedRoles: UserRole[];
  fallback?: ReactNode;
  hideIfNoAccess?: boolean;
}

/**
 * Component that conditionally renders content based on user roles
 * 
 * @param children - Content to render if user has access
 * @param allowedRoles - Array of roles that can access this content
 * @param fallback - Optional content to show if user doesn't have access
 * @param hideIfNoAccess - If true, renders nothing when user has no access (default: false)
 */
export default function RoleBasedContent({
  children,
  allowedRoles,
  fallback = null,
  hideIfNoAccess = false,
}: RoleBasedContentProps) {
  const { user, loading } = useAuth();
  
  // Don't render anything while loading
  if (loading) {
    return null;
  }
  
  const userRole = user?.role?.roleName;
  const canAccess = hasAccess(userRole, allowedRoles);
  
  if (canAccess) {
    return <>{children}</>;
  }
  
  if (hideIfNoAccess) {
    return null;
  }
  
  return <>{fallback}</>;
}

// Convenience components for common role combinations
export function SuperAdminOnly({ children, fallback, hideIfNoAccess }: Omit<RoleBasedContentProps, 'allowedRoles'>) {
  return (
    <RoleBasedContent 
      allowedRoles={['SuperAdmin']} 
      fallback={fallback}
      hideIfNoAccess={hideIfNoAccess}
    >
      {children}
    </RoleBasedContent>
  );
}

export function AdminOnly({ children, fallback, hideIfNoAccess }: Omit<RoleBasedContentProps, 'allowedRoles'>) {
  return (
    <RoleBasedContent 
      allowedRoles={['SuperAdmin', 'DepartmentAdmin']} 
      fallback={fallback}
      hideIfNoAccess={hideIfNoAccess}
    >
      {children}
    </RoleBasedContent>
  );
}

export function StaffOnly({ children, fallback, hideIfNoAccess }: Omit<RoleBasedContentProps, 'allowedRoles'>) {
  return (
    <RoleBasedContent 
      allowedRoles={['SuperAdmin', 'DepartmentAdmin', 'Officer']} 
      fallback={fallback}
      hideIfNoAccess={hideIfNoAccess}
    >
      {children}
    </RoleBasedContent>
  );
}

export function UniversityOnly({ children, fallback, hideIfNoAccess }: Omit<RoleBasedContentProps, 'allowedRoles'>) {
  return (
    <RoleBasedContent 
      allowedRoles={['University']} 
      fallback={fallback}
      hideIfNoAccess={hideIfNoAccess}
    >
      {children}
    </RoleBasedContent>
  );
}

export function StudentOnly({ children, fallback, hideIfNoAccess }: Omit<RoleBasedContentProps, 'allowedRoles'>) {
  return (
    <RoleBasedContent 
      allowedRoles={['Student']} 
      fallback={fallback}
      hideIfNoAccess={hideIfNoAccess}
    >
      {children}
    </RoleBasedContent>
  );
}
