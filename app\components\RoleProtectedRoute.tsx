"use client";

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { Container, Title, Text, Button, Alert, Box } from '@mantine/core';
import { IconShieldX, IconArrowLeft } from '@tabler/icons-react';
import { useAuth } from '../hooks/useAuth';
import { canAccessRoute, getUnauthorizedMessage, getRedirectRoute } from '../utils/routeProtection';
import { UserRole } from '../utils/rolePermissions';

interface RoleProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
  fallbackComponent?: React.ReactNode;
  redirectOnUnauthorized?: boolean;
}

export default function RoleProtectedRoute({
  children,
  allowedRoles,
  fallbackComponent,
  redirectOnUnauthorized = false,
}: RoleProtectedRouteProps) {
  const { user, loading } = useAuth();
  const pathname = usePathname();
  
  const userRole = user?.role?.roleName;
  const canAccess = allowedRoles 
    ? allowedRoles.includes(userRole as UserRole)
    : canAccessRoute(userRole, pathname);

  useEffect(() => {
    if (!loading && !canAccess && redirectOnUnauthorized) {
      const redirectRoute = getRedirectRoute(userRole);
      if (redirectRoute !== pathname) {
        window.location.href = redirectRoute;
      }
    }
  }, [loading, canAccess, redirectOnUnauthorized, userRole, pathname]);

  // Show loading state
  if (loading) {
    return (
      <Container size="sm" py="xl">
        <Box style={{ textAlign: 'center' }}>
          <Text>Loading...</Text>
        </Box>
      </Container>
    );
  }

  // Show unauthorized message if user doesn't have access
  if (!canAccess) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    return (
      <Container size="sm" py="xl">
        <Box style={{ textAlign: 'center' }}>
          <IconShieldX size={64} color="red" style={{ margin: '0 auto 1rem' }} />
          
          <Title order={2} mb="md" color="red">
            Access Denied
          </Title>
          
          <Alert color="red" mb="xl">
            <Text size="sm">
              {getUnauthorizedMessage(userRole, pathname)}
            </Text>
          </Alert>
          
          <Text mb="xl" color="dimmed">
            You don't have permission to access this page. 
            {userRole ? ` Your current role is: ${userRole}` : ' Please log in to continue.'}
          </Text>
          
          <Box style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
            <Button
              leftSection={<IconArrowLeft size={16} />}
              variant="outline"
              onClick={() => window.history.back()}
            >
              Go Back
            </Button>
            
            <Button
              onClick={() => {
                const redirectRoute = getRedirectRoute(userRole);
                window.location.href = redirectRoute;
              }}
            >
              {userRole ? 'Go to Dashboard' : 'Go to Login'}
            </Button>
          </Box>
        </Box>
      </Container>
    );
  }

  // User has access, render the protected content
  return <>{children}</>;
}

// Higher-order component for easier usage
export function withRoleProtection<P extends object>(
  Component: React.ComponentType<P>,
  allowedRoles?: UserRole[],
  options?: {
    fallbackComponent?: React.ReactNode;
    redirectOnUnauthorized?: boolean;
  }
) {
  return function ProtectedComponent(props: P) {
    return (
      <RoleProtectedRoute
        allowedRoles={allowedRoles}
        fallbackComponent={options?.fallbackComponent}
        redirectOnUnauthorized={options?.redirectOnUnauthorized}
      >
        <Component {...props} />
      </RoleProtectedRoute>
    );
  };
}
