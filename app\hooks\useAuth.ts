// hooks/useAuth.ts
import { useState, useEffect } from "react";
import { UserDetailsEndpoint } from "../utils/apiEndpoints";

interface UserData {
  userId: string;
  username: string;
  email: string;
  name: string;
  role: {
    id: string;
    roleName: string;
    description: string;
    createdAt: string;
  };
  department: {
    id: string;
    departmentName: string;
    description: string;
    createdAt: string;
  };
}

interface UseAuthReturn {
  user: UserData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUser = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log("🔍 Fetching user data from:", UserDetailsEndpoint);

      const response = await fetch(UserDetailsEndpoint, {
        method: "GET",
        credentials: "include", // Include cookies (authToken)
        headers: {
          "Content-Type": "application/json",
        },
      });

      console.log("🔍 Response status:", response.status);

      if (!response.ok) {
        if (response.status === 401) {
          // No valid token, user is not authenticated
          console.log("❌ Not authenticated");
          setUser(null);
          return;
        }

        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to fetch user data");
      }

      const data = await response.json();
      console.log("✅ User data received:", data.user);
      setUser(data.user);
    } catch (err) {
      console.error("❌ Error fetching user:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  // Fetch user data on component mount
  useEffect(() => {
    fetchUser();
  }, []);

  return {
    user,
    loading,
    error,
    refetch: fetchUser,
  };
};
